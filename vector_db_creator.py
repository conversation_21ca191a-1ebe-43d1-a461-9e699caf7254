import json
import os
import sys
from typing import List, Dict, Optional, Any
import ollama
import asyncio
from abc import ABC, abstractmethod
import time
import hashlib
from pathlib import Path
from datetime import datetime

try:
    import chromadb
    from chromadb.utils import embedding_functions
except ImportError as e:
    print(f"ChromaDB import error: {e}")
    sys.exit(1)

# Framework imports for full integration (Phase 0)
from processing_pipeline import ProcessingStage

class OllamaEmbeddingFunction:
    """Enhanced embedding function that uses Ollama for generating embeddings with better error handling"""

    def __init__(self, ollama_host: Optional[str] = None, model_name: str = "nomic-embed-text"):
        # Use environment variable if not provided, with fallback for different environments
        if ollama_host is None:
            ollama_host = os.getenv('OLLAMA_HOST', 'http://ollama:11434')  # Docker default
            # Fallback for non-Docker environments
            if ollama_host == 'http://ollama:11434' and not self._is_docker_environment():
                ollama_host = 'http://home-ai-server.local:11434'

        self.ollama_host = ollama_host
        self.model_name = model_name
        self.client = ollama.Client(host=ollama_host)
        self.embedding_dim = None  # Will be detected on first use

        # Test connection and ensure model is available
        try:
            # Try to pull the embedding model if it's not available
            self.client.pull(model_name)
            print(f"✅ Ollama embedding model '{model_name}' is ready")

            # Test embedding to detect dimensions
            test_response = self.client.embeddings(model=model_name, prompt="test")
            self.embedding_dim = len(test_response.get("embedding", []))
            print(f"✅ Detected embedding dimensions: {self.embedding_dim}")

        except Exception as e:
            print(f"⚠️ Warning: Could not initialize model '{model_name}': {e}")
            print("   Make sure the model is available in Ollama")
            # Default to nomic-embed-text dimensions
            self.embedding_dim = 384

    def _is_docker_environment(self) -> bool:
        """Check if running inside Docker container"""
        try:
            # Check for Docker-specific files/environment
            return (os.path.exists('/.dockerenv') or
                   os.getenv('DOCKER_CONTAINER') == 'true' or
                   'docker' in os.getenv('HOSTNAME', '').lower())
        except Exception:
            return False

    def __call__(self, input: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts with enhanced error handling"""
        embeddings = []

        for i, text in enumerate(input):
            try:
                # Truncate very long texts to avoid token limits
                if len(text) > 8000:  # Conservative limit
                    text = text[:8000] + "..."
                    print(f"⚠️ Truncated long text for embedding (chunk {i+1})")
                
                response = self.client.embeddings(model=self.model_name, prompt=text)
                embedding = response["embedding"]

                # Update dimension if not set
                if self.embedding_dim is None:
                    self.embedding_dim = len(embedding)
                    print(f"✅ Detected embedding dimensions: {self.embedding_dim}")

                embeddings.append(embedding)
                
                # Progress indicator for large batches
                if (i + 1) % 10 == 0:
                    print(f"   Generated embeddings for {i + 1}/{len(input)} texts")
                    
            except Exception as e:
                print(f"❌ Error generating embedding for text {i+1}: {e}")
                # Return a zero vector as fallback with correct dimensions
                fallback_dim = self.embedding_dim if self.embedding_dim else 384
                embeddings.append([0.0] * fallback_dim)

        return embeddings

class EnhancedVectorDBCreator:
    """Enhanced vector database creator with support for rich metadata and optimized processing"""
    
    def __init__(self, db_path="./chroma_db", ollama_host=None, use_ollama=True):
        """Initialize the enhanced vector database creator with optional Ollama embeddings"""
        # Use environment variable if not provided, with fallback for different environments
        if ollama_host is None:
            ollama_host = os.getenv('OLLAMA_HOST', 'http://ollama:11434')  # Docker default
            # Fallback for non-Docker environments
            if ollama_host == 'http://ollama:11434' and not self._is_docker_environment():
                ollama_host = 'http://home-ai-server.local:11434'

        try:
            self.client = chromadb.PersistentClient(path=db_path)
            self.collection_name = "enhanced_code_collection"  # Default, will be updated
            self.use_ollama = use_ollama
            self.ollama_host = ollama_host
            
            # Set up embedding function
            if use_ollama:
                print(f"🔥 Initializing Enhanced VectorDB with Ollama embeddings (host: {ollama_host})")
                self.embedding_function = OllamaEmbeddingFunction(
                    ollama_host=ollama_host,
                    model_name="nomic-embed-text"  # Good embedding model for code
                )
            else:
                print("🔥 Using ChromaDB default embeddings for Enhanced VectorDB")
                self.embedding_function = embedding_functions.DefaultEmbeddingFunction()
            
            print(f"Initialized Enhanced ChromaDB client at {db_path}")
        except Exception as e:
            print(f"Error initializing Enhanced ChromaDB client: {e}")
            raise

    def _is_docker_environment(self) -> bool:
        """Check if running inside Docker container"""
        try:
            # Check for Docker-specific files/environment
            return (os.path.exists('/.dockerenv') or
                   os.getenv('DOCKER_CONTAINER') == 'true' or
                   'docker' in os.getenv('HOSTNAME', '').lower())
        except Exception:
            return False

    def create_collection(self, chunks: List[Dict], collection_name: Optional[str] = None):
        """Create ChromaDB collection with enhanced metadata support and optimized processing"""
        
        if collection_name:
            self.collection_name = collection_name
        
        # Delete existing collection if it exists
        try:
            self.client.delete_collection(self.collection_name)
            print(f"Deleted existing collection: {self.collection_name}")
        except Exception:
            print(f"No existing collection to delete: {self.collection_name}")
        
        # Create new collection with enhanced metadata
        try:
            collection = self.client.create_collection(
                name=self.collection_name,
                embedding_function=self.embedding_function,
                metadata={
                    "description": "Enhanced multi-language code with semantic metadata, quality analysis, and complexity metrics",
                    "version": "4.0_framework_integrated",
                    "features": "semantic_tags,quality_analysis,complexity_metrics,api_surface_analysis,framework_integration,gpu_processing",
                    "languages": "C,C++,Python,C#,JavaScript,TypeScript,Rust,Java,Go,SQL,TCL,Verilog,Bash,CommonLisp,EmacsLisp,Scheme,Lua,Make,JSON,YAML,XML,PHP,Perl,Markdown,HTML,Fortran,VHDL,Metta,Prolog,Assembly,MATLAB,R,CMake,TeX,Pascal,TOML,SCSS,CSS,Swift,Kotlin,Scala,Groovy,Haskell,Erlang,Elixir,Clojure",
                    "framework_version": "language_agnostic_v1.0",
                    "supported_chunk_types": "code_implementation,architectural_pattern,system_design"
                }
            )
            print(f"Created enhanced collection: {self.collection_name}")
            if self.use_ollama:
                print("Using Ollama embeddings with nomic-embed-text model")
            else:
                print("Using ChromaDB's default embedding function")
        except Exception as e:
            print(f"Error creating enhanced collection: {e}")
            raise
        
        # Prepare data for insertion with enhanced content formatting
        documents = []
        metadatas = []
        ids = []
        
        print(f"Preparing {len(chunks)} chunks with enhanced metadata...")
        
        for i, chunk in enumerate(chunks):
            try:
                # Enhanced document content with rich metadata context
                content = self._format_enhanced_document_content(chunk)
                documents.append(content)
                
                # Ensure metadata is ChromaDB compatible with enhanced fields
                metadata = self._flatten_enhanced_metadata(chunk['metadata'])
                metadatas.append(metadata)
                
                # Create more descriptive IDs with enhanced information
                chunk_id = chunk['metadata'].get('chunk_id', f'chunk_{i}')
                chunk_type = chunk['metadata'].get('type', 'unknown')
                language = chunk['metadata'].get('language', 'unknown')
                ids.append(f"{language}_{chunk_type}_{i}_{chunk_id}")
                
            except Exception as e:
                print(f"❌ Error preparing chunk {i}: {e}")
                # Skip problematic chunks but continue processing
                continue
        
        print(f"Adding {len(documents)} enhanced documents to collection...")
        if self.use_ollama:
            print("Generating embeddings using Ollama (this may take a while for large codebases)...")
        else:
            print("ChromaDB will automatically generate embeddings")
        
        # Show enhanced statistics before insertion
        self._show_enhanced_chunk_statistics(chunks)
        
        # Process in smaller batches for enhanced metadata (more processing per chunk)
        batch_size = 25 if self.use_ollama else 50  # Smaller batches for Ollama due to enhanced content
        total_batches = (len(documents) - 1) // batch_size + 1
        successful_batches = 0
        failed_chunks = []
        total_processed = 0
        
        for i in range(0, len(documents), batch_size):
            batch_docs = documents[i:i+batch_size]
            batch_metas = metadatas[i:i+batch_size]
            batch_ids = ids[i:i+batch_size]
            
            batch_num = i // batch_size + 1
            print(f"Processing enhanced batch {batch_num}/{total_batches} ({len(batch_docs)} documents)")
            
            try:
                # ChromaDB will use our enhanced embedding function
                collection.add(
                    documents=batch_docs,
                    metadatas=batch_metas,
                    ids=batch_ids
                )
                print(f"✓ Successfully added enhanced batch {batch_num}")
                successful_batches += 1
                total_processed += len(batch_docs)
                
                # Progress indicator
                progress = (total_processed / len(documents)) * 100
                print(f"   Progress: {progress:.1f}% ({total_processed}/{len(documents)} documents)")
                
            except Exception as e:
                print(f"✗ Error processing enhanced batch {batch_num}: {e}")
                # Track failed chunks for debugging
                for j, chunk_id in enumerate(batch_ids):
                    failed_chunks.append({
                        'id': chunk_id,
                        'error': str(e)[:200],  # Truncate long errors
                        'batch': batch_num,
                        'metadata_keys': list(batch_metas[j].keys()) if j < len(batch_metas) else []
                    })
                continue
        
        final_count = collection.count()
        print("✅ Enhanced collection created successfully!")
        print(f"   Successful batches: {successful_batches}/{total_batches}")
        print(f"   Total documents in collection: {final_count:,}")
        print("   Enhanced features: Semantic tags, quality analysis, complexity metrics")
        
        if failed_chunks:
            print(f"⚠ Warning: {len(failed_chunks)} chunks failed to insert")
            # Save failed chunks for debugging
            try:
                with open(f"failed_chunks_{self.collection_name}.json", "w") as f:
                    json.dump(failed_chunks, f, indent=2)
                print(f"Failed chunk details saved to failed_chunks_{self.collection_name}.json")
            except Exception as e:
                print(f"Could not save failed chunks file: {e}")
        
        # Validate enhanced metadata in collection
        self._validate_enhanced_metadata(collection)
        
        return collection
    
    def _format_enhanced_document_content(self, chunk: Dict) -> str:
        """Format document content with rich metadata context for enhanced semantic search"""
        metadata = chunk['metadata']
        content = chunk['content']
        
        # Enhanced contextual information for better embeddings
        context_parts = []
        
        # Add file and location context
        rel_path = metadata.get('relative_path', metadata.get('filepath', ''))
        if rel_path:
            context_parts.append(f"File: {rel_path}")
        
        # Add line range for precise location
        start_line = metadata.get('start_line')
        end_line = metadata.get('end_line')
        if start_line and end_line:
            context_parts.append(f"Lines: {start_line}-{end_line}")
        
        # Add language and type context
        language = metadata.get('language', 'unknown')
        chunk_type = metadata.get('type', 'unknown')
        context_parts.append(f"Language: {language.upper()}")
        context_parts.append(f"Type: {chunk_type}")
        
        # Add enhanced metadata context
        
        # Semantic tags for domain context
        semantic_tags = metadata.get('semantic_tags', [])
        if isinstance(semantic_tags, str):
            try:
                semantic_tags = json.loads(semantic_tags)
            except (json.JSONDecodeError, ValueError):
                semantic_tags = []
        if semantic_tags:
            context_parts.append(f"Domains: {', '.join(semantic_tags[:3])}")  # Top 3 tags
        
        # Quality indicators
        quality_indicators = metadata.get('quality_indicators', {})
        if isinstance(quality_indicators, str):
            try:
                quality_indicators = json.loads(quality_indicators)
            except (json.JSONDecodeError, ValueError, TypeError):
                quality_indicators = {}
        
        quality_score = quality_indicators.get('maintainability_score', 'unknown')
        if quality_score != 'unknown':
            context_parts.append(f"Quality: {quality_score}")
        
        # Complexity information
        complexity_metrics = metadata.get('complexity_metrics', {})
        if isinstance(complexity_metrics, str):
            try:
                complexity_metrics = json.loads(complexity_metrics)
            except json.JSONDecodeError:
                complexity_metrics = {}
        
        complexity_score = complexity_metrics.get('complexity_score', 'unknown')
        if complexity_score != 'unknown':
            context_parts.append(f"Complexity: {complexity_score}")
        
        # API surface information
        api_surface = metadata.get('api_surface', {})
        if isinstance(api_surface, str):
            try:
                api_surface = json.loads(api_surface)
            except (json.JSONDecodeError, ValueError):
                api_surface = {}
        
        visibility = api_surface.get('visibility', 'unknown')
        if visibility != 'unknown':
            context_parts.append(f"Visibility: {visibility}")
        
        # Add specific identifiers with enhanced context
        if chunk_type == 'function' and 'function_name' in metadata:
            function_name = metadata['function_name']
            context_parts.append(f"Function: {function_name}")
            
            # Add parameter information if available
            param_count = complexity_metrics.get('parameter_count', 0)
            if param_count > 0:
                context_parts.append(f"Parameters: {param_count}")
                
        elif chunk_type == 'class' and 'class_name' in metadata:
            class_name = metadata['class_name']
            context_parts.append(f"Class: {class_name}")
            
            # Add method count if available
            method_count = metadata.get('method_count', 0)
            if method_count > 0:
                context_parts.append(f"Methods: {method_count}")
                
        elif chunk_type == 'method':
            class_name = metadata.get('class_name', 'Unknown')
            method_name = metadata.get('method_name', 'Unknown')
            context_parts.append(f"Method: {class_name}::{method_name}")
            
        elif chunk_type == 'namespace' and 'namespace_name' in metadata:
            namespace_name = metadata['namespace_name']
            context_parts.append(f"Namespace: {namespace_name}")
        
        # Add code patterns for better searchability
        code_patterns = metadata.get('code_patterns', [])
        if isinstance(code_patterns, str):
            try:
                code_patterns = json.loads(code_patterns)
            except (json.JSONDecodeError, ValueError):
                code_patterns = []
        if code_patterns:
            context_parts.append(f"Patterns: {', '.join(code_patterns[:2])}")  # Top 2 patterns
        
        # Combine enhanced context with content
        context_header = " | ".join(context_parts)
        
        # Create enhanced document with structured metadata
        enhanced_content = f"""=== CODE METADATA ===
{context_header}

=== SOURCE CODE ===
{content}

=== SEARCHABLE TERMS ==="""
        
        # Add searchable terms based on metadata
        searchable_terms = []
        
        # Add function/class names as searchable terms
        if 'function_name' in metadata:
            searchable_terms.append(f"function_{metadata['function_name']}")
        if 'class_name' in metadata:
            searchable_terms.append(f"class_{metadata['class_name']}")
        if 'method_name' in metadata:
            searchable_terms.append(f"method_{metadata['method_name']}")
        
        # Add semantic tags as searchable terms
        for tag in semantic_tags:
            searchable_terms.append(f"domain_{tag}")
        
        # Add quality and complexity as searchable terms
        if quality_score != 'unknown':
            searchable_terms.append(f"quality_{quality_score}")
        if complexity_score != 'unknown':
            searchable_terms.append(f"complexity_{complexity_score}")
        
        # Add language-specific searchable terms
        if language == 'c':
            if any(keyword in content.lower() for keyword in ['malloc', 'free']):
                searchable_terms.append("memory_management")
            if any(keyword in content.lower() for keyword in ['socket', 'bind', 'listen']):
                searchable_terms.append("network_programming")
        elif language == 'cpp':
            if any(keyword in content.lower() for keyword in ['new', 'delete', 'malloc', 'free']):
                searchable_terms.append("memory_management")
            if any(keyword in content.lower() for keyword in ['class', 'template', 'namespace']):
                searchable_terms.append("oop_features")
            if any(keyword in content.lower() for keyword in ['socket', 'bind', 'listen']):
                searchable_terms.append("network_programming")
        elif language == 'python':
            if any(keyword in content.lower() for keyword in ['async', 'await']):
                searchable_terms.append("async_programming")
            if any(keyword in content.lower() for keyword in ['import', 'from']):
                searchable_terms.append("module_usage")
        elif language == 'csharp':
            if any(keyword in content.lower() for keyword in ['linq', 'select', 'where']):
                searchable_terms.append("linq_usage")
            if any(keyword in content.lower() for keyword in ['async', 'task']):
                searchable_terms.append("async_programming")
        elif language == 'javascript':
            if any(keyword in content.lower() for keyword in ['async', 'await', 'promise']):
                searchable_terms.append("async_programming")
            if any(keyword in content.lower() for keyword in ['react', 'component', 'jsx']):
                searchable_terms.append("react_framework")
            if any(keyword in content.lower() for keyword in ['node', 'express', 'server']):
                searchable_terms.append("nodejs_backend")
        elif language == 'typescript':
            if any(keyword in content.lower() for keyword in ['interface', 'type', 'generic']):
                searchable_terms.append("type_system")
            if any(keyword in content.lower() for keyword in ['async', 'await', 'promise']):
                searchable_terms.append("async_programming")
            if any(keyword in content.lower() for keyword in ['react', 'component', 'tsx']):
                searchable_terms.append("react_framework")
        elif language == 'rust':
            if any(keyword in content.lower() for keyword in ['unsafe', 'raw', 'ptr']):
                searchable_terms.append("unsafe_code")
            if any(keyword in content.lower() for keyword in ['async', 'await', 'tokio']):
                searchable_terms.append("async_programming")
            if any(keyword in content.lower() for keyword in ['trait', 'impl', 'generic']):
                searchable_terms.append("trait_system")
        elif language == 'java':
            if any(keyword in content.lower() for keyword in ['spring', 'annotation', '@']):
                searchable_terms.append("spring_framework")
            if any(keyword in content.lower() for keyword in ['thread', 'concurrent', 'executor']):
                searchable_terms.append("concurrency")
            if any(keyword in content.lower() for keyword in ['interface', 'abstract', 'extends', 'implements', 'class']):
                searchable_terms.append("oop_patterns")
        elif language == 'go':
            if any(keyword in content.lower() for keyword in ['goroutine', 'channel', 'select', 'go ', 'chan', 'make(chan']):
                searchable_terms.append("concurrency")
            if any(keyword in content.lower() for keyword in ['gin', 'echo', 'fiber', 'net/http']):
                searchable_terms.append("web_framework")
            if any(keyword in content.lower() for keyword in ['context', 'sync', 'mutex']):
                searchable_terms.append("synchronization")
        elif language == 'sql':
            if any(keyword in content.lower() for keyword in ['select', 'join', 'where']):
                searchable_terms.append("query_operations")
            if any(keyword in content.lower() for keyword in ['create', 'alter', 'drop']):
                searchable_terms.append("ddl_operations")
            if any(keyword in content.lower() for keyword in ['insert', 'update', 'delete']):
                searchable_terms.append("dml_operations")
        elif language == 'bash':
            if any(keyword in content.lower() for keyword in ['function', 'if', 'for', 'while']):
                searchable_terms.append("shell_scripting")
            if any(keyword in content.lower() for keyword in ['grep', 'sed', 'awk']):
                searchable_terms.append("text_processing")
        elif language == 'php':
            if any(keyword in content.lower() for keyword in ['class', 'extends', 'implements']):
                searchable_terms.append("oop_patterns")
            if any(keyword in content.lower() for keyword in ['$_get', '$_post', '$_session']):
                searchable_terms.append("web_development")
        elif language == 'html':
            if any(keyword in content.lower() for keyword in ['form', 'input', 'button']):
                searchable_terms.append("forms")
            if any(keyword in content.lower() for keyword in ['div', 'span', 'section']):
                searchable_terms.append("layout")
        elif language == 'verilog':
            if any(keyword in content.lower() for keyword in ['always', 'posedge', 'negedge']):
                searchable_terms.append("sequential_logic")
            if any(keyword in content.lower() for keyword in ['assign', 'wire']):
                searchable_terms.append("combinational_logic")
        elif language == 'vhdl':
            if any(keyword in content.lower() for keyword in ['process', 'clk', 'reset']):
                searchable_terms.append("sequential_logic")
            if any(keyword in content.lower() for keyword in ['entity', 'architecture']):
                searchable_terms.append("design_units")
            if any(keyword in content.lower() for keyword in ['signal', 'variable', 'port']):
                searchable_terms.append("signal_processing")
        elif language == 'tcl':
            if any(keyword in content.lower() for keyword in ['proc', 'namespace']):
                searchable_terms.append("procedures")
            if any(keyword in content.lower() for keyword in ['string', 'regexp', 'list']):
                searchable_terms.append("data_processing")
        elif language == 'commonlisp':
            if any(keyword in content.lower() for keyword in ['defun', 'lambda']):
                searchable_terms.append("functional_programming")
            if any(keyword in content.lower() for keyword in ['defmacro', 'macro']):
                searchable_terms.append("macro_programming")
            if any(keyword in content.lower() for keyword in ['defclass', 'defmethod']):
                searchable_terms.append("object_oriented")
        elif language == 'elisp':
            if any(keyword in content.lower() for keyword in ['defun', 'lambda']):
                searchable_terms.append("functional_programming")
            if any(keyword in content.lower() for keyword in ['buffer', 'current-buffer']):
                searchable_terms.append("buffer_management")
            if any(keyword in content.lower() for keyword in ['hook', 'keymap']):
                searchable_terms.append("emacs_customization")
        elif language == 'scheme':
            if any(keyword in content.lower() for keyword in ['define', 'lambda']):
                searchable_terms.append("functional_programming")
            if any(keyword in content.lower() for keyword in ['call/cc', 'continuation']):
                searchable_terms.append("continuation_programming")
        elif language == 'lua':
            if any(keyword in content.lower() for keyword in ['function', 'local']):
                searchable_terms.append("scripting")
            if any(keyword in content.lower() for keyword in ['table', 'metatable']):
                searchable_terms.append("table_programming")
            if any(keyword in content.lower() for keyword in ['coroutine', 'yield']):
                searchable_terms.append("coroutine_programming")
        elif language == 'make':
            if any(keyword in content.lower() for keyword in ['target', 'phony']):
                searchable_terms.append("build_automation")
            if any(keyword in content.lower() for keyword in ['ifdef', 'ifndef', 'ifeq']):
                searchable_terms.append("conditional_build")
        elif language == 'json':
            if any(keyword in content.lower() for keyword in ['object', 'array']):
                searchable_terms.append("data_structure")
            if any(keyword in content.lower() for keyword in ['schema', 'validation']):
                searchable_terms.append("data_validation")
        elif language == 'yaml':
            if any(keyword in content.lower() for keyword in ['mapping', 'sequence']):
                searchable_terms.append("data_structure")
            if any(keyword in content.lower() for keyword in ['anchor', 'alias']):
                searchable_terms.append("yaml_features")
        elif language == 'xml':
            if any(keyword in content.lower() for keyword in ['element', 'attribute']):
                searchable_terms.append("markup_structure")
            if any(keyword in content.lower() for keyword in ['namespace', 'schema']):
                searchable_terms.append("xml_features")
        elif language == 'perl':
            if any(keyword in content.lower() for keyword in ['my', 'our', 'local']):
                searchable_terms.append("variable_scoping")
            if any(keyword in content.lower() for keyword in ['regex', 'match', 'substitute', '=~', 's/', 'm/']):
                searchable_terms.append("regex_processing")
            if any(keyword in content.lower() for keyword in ['ref', 'deref', '\\$', '\\@', '\\%']):
                searchable_terms.append("reference_programming")
        elif language == 'markdown':
            if any(keyword in content.lower() for keyword in ['header', 'h1', 'h2']):
                searchable_terms.append("documentation_structure")
            if any(keyword in content.lower() for keyword in ['link', 'image', 'code']):
                searchable_terms.append("content_formatting")
        elif language == 'fortran':
            if any(keyword in content.lower() for keyword in ['program', 'subroutine', 'function']):
                searchable_terms.append("program_units")
            if any(keyword in content.lower() for keyword in ['module', 'use', 'interface']):
                searchable_terms.append("modular_programming")
            if any(keyword in content.lower() for keyword in ['real', 'complex', 'dimension']):
                searchable_terms.append("scientific_computing")

        if searchable_terms:
            enhanced_content += f"\n{' '.join(searchable_terms)}"
        
        return enhanced_content
    
    def _flatten_enhanced_metadata(self, metadata: Dict) -> Dict:
        """Flatten enhanced metadata to ensure ChromaDB compatibility while preserving rich information"""
        flattened = {}
        
        # Enhanced metadata flattening with better handling
        for key, value in metadata.items():
            if isinstance(value, (str, int, float, bool)):
                flattened[key] = value
            elif value is None:
                flattened[key] = ""
            elif isinstance(value, (list, dict)):
                # Convert complex types to JSON strings with error handling
                try:
                    flattened[key] = json.dumps(value, ensure_ascii=False)
                except Exception as e:
                    print(f"⚠️ Warning: Could not serialize metadata field '{key}': {e}")
                    flattened[key] = str(value)[:500]  # Truncate long values
            else:
                # Convert other types to strings with length limit
                str_value = str(value)
                flattened[key] = str_value[:500] if len(str_value) > 500 else str_value
        
        # Ensure required fields exist
        required_fields = ['type', 'language', 'relative_path']
        for field in required_fields:
            if field not in flattened:
                flattened[field] = 'unknown'
        
        # Add enhanced metadata indicators
        flattened['has_enhanced_metadata'] = True
        flattened['metadata_version'] = '3.0_enhanced'
        
        return flattened
    
    def _show_enhanced_chunk_statistics(self, chunks: List[Dict]):
        """Display comprehensive statistics about the enhanced chunks being processed"""
        print("\n=== Enhanced Chunk Statistics ===")
        
        # Enhanced counters
        type_counts: Dict[str, int] = {}
        language_counts: Dict[str, int] = {}
        file_counts: Dict[str, int] = {}
        complexity_counts: Dict[str, int] = {}
        quality_counts: Dict[str, int] = {}
        semantic_tag_frequency: Dict[str, int] = {}
        pattern_frequency: Dict[str, int] = {}
        
        total_lines = 0
        documented_chunks = 0
        public_api_chunks = 0
        
        for chunk in chunks:
            metadata = chunk['metadata']
            
            # Basic counts
            chunk_type = metadata.get('type', 'unknown')
            type_counts[chunk_type] = type_counts.get(chunk_type, 0) + 1
            
            language = metadata.get('language', 'unknown')
            language_counts[language] = language_counts.get(language, 0) + 1
            
            filepath = metadata.get('relative_path', metadata.get('filepath', 'unknown'))
            file_counts[filepath] = file_counts.get(filepath, 0) + 1
            
            # Enhanced metadata analysis
            
            # Complexity analysis
            complexity_metrics = metadata.get('complexity_metrics', {})
            if isinstance(complexity_metrics, dict):
                complexity_score = complexity_metrics.get('complexity_score', 'unknown')
                complexity_counts[complexity_score] = complexity_counts.get(complexity_score, 0) + 1
                
                # Line count
                line_count = complexity_metrics.get('line_count', 0)
                total_lines += line_count
            
            # Quality analysis
            quality_indicators = metadata.get('quality_indicators', {})
            if isinstance(quality_indicators, dict):
                quality_score = quality_indicators.get('maintainability_score', 'unknown')
                quality_counts[quality_score] = quality_counts.get(quality_score, 0) + 1
                
                # Documentation tracking
                if quality_indicators.get('has_documentation', False):
                    documented_chunks += 1
            
            # Semantic tags analysis
            semantic_tags = metadata.get('semantic_tags', [])
            if isinstance(semantic_tags, list):
                for tag in semantic_tags:
                    semantic_tag_frequency[tag] = semantic_tag_frequency.get(tag, 0) + 1
            
            # Code patterns analysis
            code_patterns = metadata.get('code_patterns', [])
            if isinstance(code_patterns, list):
                for pattern in code_patterns:
                    pattern_frequency[pattern] = pattern_frequency.get(pattern, 0) + 1
            
            # API surface analysis
            api_surface = metadata.get('api_surface', {})
            if isinstance(api_surface, dict):
                if api_surface.get('is_public', False):
                    public_api_chunks += 1
        
        print(f"Total enhanced chunks: {len(chunks):,}")
        print(f"Total lines of code: {total_lines:,}")
        if len(chunks) > 0:
            print(f"Average chunk size: {total_lines / len(chunks):.1f} lines")
        else:
            print("Average chunk size: N/A (no chunks processed)")
        
        print("\n📊 Enhanced Metadata Coverage:")
        if len(chunks) > 0:
            print(f"  Documented chunks: {documented_chunks:,} ({documented_chunks/len(chunks)*100:.1f}%)")
            print(f"  Public API chunks: {public_api_chunks:,} ({public_api_chunks/len(chunks)*100:.1f}%)")
        else:
            print("  No chunks available for metadata analysis")
        
        print("\n🏗️ Code Structure Types:")
        if len(chunks) > 0:
            for chunk_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / len(chunks)) * 100
                print(f"  {chunk_type}: {count:,} ({percentage:.1f}%)")
        else:
            print("  No code structure types detected")
        
        print("\n💻 Programming Languages:")
        if len(chunks) > 0:
            for language, count in sorted(language_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / len(chunks)) * 100
                print(f"  {language.upper()}: {count:,} ({percentage:.1f}%)")
        else:
            print("  No programming languages detected")
        
        if complexity_counts:
            print("\n⚡ Complexity Distribution:")
            complexity_order = ['low', 'medium', 'high', 'very_high', 'unknown']
            for complexity in complexity_order:
                if complexity in complexity_counts:
                    count = complexity_counts[complexity]
                    if len(chunks) > 0:
                        percentage = (count / len(chunks)) * 100
                        emoji = {'low': '🟢', 'medium': '🟡', 'high': '🟠', 'very_high': '🔴', 'unknown': '⚪'}[complexity]
                        print(f"  {emoji} {complexity}: {count:,} ({percentage:.1f}%)")
                    else:
                        emoji = {'low': '🟢', 'medium': '🟡', 'high': '🟠', 'very_high': '🔴', 'unknown': '⚪'}[complexity]
                        print(f"  {emoji} {complexity}: {count:,} (N/A%)")
        
        if quality_counts:
            print("\n✨ Quality Distribution:")
            quality_order = ['excellent', 'good', 'fair', 'poor', 'unknown']
            for quality in quality_order:
                if quality in quality_counts:
                    count = quality_counts[quality]
                    if len(chunks) > 0:
                        percentage = (count / len(chunks)) * 100
                        emoji = {'excellent': '✨', 'good': '🟢', 'fair': '🟡', 'poor': '🔴', 'unknown': '⚪'}[quality]
                        print(f"  {emoji} {quality}: {count:,} ({percentage:.1f}%)")
                    else:
                        emoji = {'excellent': '✨', 'good': '🟢', 'fair': '🟡', 'poor': '🔴', 'unknown': '⚪'}[quality]
                        print(f"  {emoji} {quality}: {count:,} (N/A%)")
        
        if semantic_tag_frequency:
            print("\n🏷️ Top Semantic Categories:")
            sorted_tags = sorted(semantic_tag_frequency.items(), key=lambda x: x[1], reverse=True)
            for tag, count in sorted_tags[:10]:  # Top 10 tags
                if len(chunks) > 0:
                    percentage = (count / len(chunks)) * 100
                    display_tag = tag.replace('_', ' ').title()
                    print(f"  • {display_tag}: {count:,} ({percentage:.1f}%)")
                else:
                    display_tag = tag.replace('_', ' ').title()
                    print(f"  • {display_tag}: {count:,} (N/A%)")
        
        if pattern_frequency:
            print("\n🎨 Top Code Patterns:")
            sorted_patterns = sorted(pattern_frequency.items(), key=lambda x: x[1], reverse=True)
            for pattern, count in sorted_patterns[:8]:  # Top 8 patterns
                if len(chunks) > 0:
                    percentage = (count / len(chunks)) * 100
                    display_pattern = pattern.replace('_', ' ').title()
                    print(f"  • {display_pattern}: {count:,} ({percentage:.1f}%)")
                else:
                    display_pattern = pattern.replace('_', ' ').title()
                    print(f"  • {display_pattern}: {count:,} (N/A%)")
        
        print(f"\n📁 Files processed: {len(file_counts):,}")
        print("=" * 50 + "\n")
    
    def _validate_enhanced_metadata(self, collection):
        """Validate that enhanced metadata was properly stored in the collection"""
        print("🔍 Validating enhanced metadata in collection...")
        
        try:
            # Get a sample to check metadata structure
            sample = collection.get(limit=3, include=['metadatas'])
            
            if not sample['metadatas']:
                print("⚠️ Warning: No metadata found in collection")
                return
            
            enhanced_features_found = set()
            metadata_issues = []
            
            for i, metadata in enumerate(sample['metadatas']):
                sample_id = f"sample_{i+1}"
                
                # Check for enhanced metadata fields
                enhanced_fields = [
                    'semantic_tags', 'complexity_metrics', 'quality_indicators',
                    'code_patterns', 'api_surface', 'dependencies'
                ]
                
                found_fields = [field for field in enhanced_fields if field in metadata]
                enhanced_features_found.update(found_fields)
                
                # Check metadata version
                metadata_version = metadata.get('metadata_version', 'unknown')
                has_enhanced_flag = metadata.get('has_enhanced_metadata', False)
                
                if not has_enhanced_flag:
                    metadata_issues.append(f"{sample_id}: Missing enhanced metadata flag")
                
                if metadata_version not in ['3.0_enhanced', '4.0_framework_integrated']:
                    metadata_issues.append(f"{sample_id}: Unexpected metadata version: {metadata_version}")
                
                # Validate JSON fields can be parsed
                for field in ['semantic_tags', 'complexity_metrics', 'quality_indicators']:
                    if field in metadata:
                        try:
                            if isinstance(metadata[field], str):
                                json.loads(metadata[field])
                        except json.JSONDecodeError:
                            metadata_issues.append(f"{sample_id}: Invalid JSON in {field}")
            
            # Report validation results
            print("✅ Validation complete:")
            print(f"   Enhanced features found: {len(enhanced_features_found)}/6")
            print(f"   Features detected: {', '.join(sorted(enhanced_features_found))}")
            
            if metadata_issues:
                print("⚠️ Validation issues found:")
                for issue in metadata_issues:
                    print(f"   • {issue}")
            else:
                print("✅ All enhanced metadata validated successfully")
                
        except Exception as e:
            print(f"❌ Error during metadata validation: {e}")

    def integrate_with_framework(self, framework_components: Dict[str, Any]) -> bool:
        """Integrate with the new Language-Agnostic Framework components"""
        try:
            print("🔗 Integrating VectorDB with Language-Agnostic Framework...")

            # Store framework component references
            self.language_registry = framework_components.get('language_registry')
            self.chunk_registry = framework_components.get('chunk_registry')
            self.gpu_manager = framework_components.get('gpu_manager')

            # Update supported languages from framework
            if self.language_registry:
                supported_languages = self.language_registry.get_supported_languages()
                print(f"✅ Framework integration: {len(supported_languages)} languages available")

                # Update collection metadata with actual framework languages
                self._update_language_metadata(supported_languages)

            # Update chunk types from framework
            if self.chunk_registry:
                chunk_types = list(self.chunk_registry.get_registered_types())
                print(f"✅ Framework integration: {len(chunk_types)} chunk types available")

                # Update collection metadata with actual chunk types
                self._update_chunk_metadata(chunk_types)

            # Check GPU availability for large embedding operations
            if self.gpu_manager:
                try:
                    available_gpus = asyncio.run(self.gpu_manager.discover_available_gpus())
                    if available_gpus:
                        print(f"✅ Framework integration: {len(available_gpus)} GPUs available for processing")
                        self._setup_gpu_distributed_processing(available_gpus)
                except Exception as e:
                    print(f"⚠️ GPU status check failed: {e}")

            print("✅ VectorDB framework integration complete")
            return True

        except Exception as e:
            print(f"❌ Framework integration failed: {e}")
            return False

    def _update_language_metadata(self, supported_languages: List[str]) -> None:
        """Update collection metadata with actual framework languages"""
        self.framework_languages = supported_languages
        self.language_count = len(supported_languages)

    def _update_chunk_metadata(self, chunk_types: List[str]) -> None:
        """Update collection metadata with actual framework chunk types"""
        self.framework_chunk_types = chunk_types
        self.chunk_type_count = len(chunk_types)

    def _setup_gpu_distributed_processing(self, available_gpus: Dict[str, Any]) -> None:
        """Setup GPU-distributed processing capabilities"""
        self.available_gpus = available_gpus
        self.gpu_processing_enabled = True

        # Categorize GPUs by capability for optimal distribution
        self.gpu_tiers: dict[str, list[tuple[str, dict[str, Any]]]] = {}
        for host, gpu_info in available_gpus.items():
            tier = gpu_info.get('tier', 'unknown')
            if tier not in self.gpu_tiers:
                self.gpu_tiers[tier] = []
            self.gpu_tiers[tier].append((host, gpu_info))

    def create_framework_compatible_collection(self, chunks: List[Dict], collection_name: Optional[str] = None, framework_metadata: Optional[Dict] = None):
        """Create collection with enhanced framework compatibility"""
        if framework_metadata:
            # Update collection metadata with framework information
            if hasattr(self, 'language_registry') and self.language_registry:
                framework_metadata['framework_languages'] = len(self.language_registry.get_supported_languages())

            if hasattr(self, 'chunk_registry') and self.chunk_registry:
                framework_metadata['framework_chunk_types'] = list(self.chunk_registry.get_registered_types())

        # Use the existing enhanced collection creation with framework metadata
        return self.create_collection(chunks, collection_name)

class VectorDBPipelineStage(ProcessingStage):
    """VectorDB creation as a processing pipeline stage (Phase 0 Integration)"""

    def __init__(self, collection_name: Optional[str] = None,
                 embedding_model: str = "nomic-embed-text",
                 gpu_distributed: bool = True):
        self.collection_name = collection_name
        self.embedding_model = embedding_model
        self.gpu_distributed = gpu_distributed
        self.vector_db_creator: Optional['EnhancedVectorDBCreator'] = None
        self.gpu_manager = None
        self.language_registry = None
        self.chunk_registry = None

    def get_stage_name(self) -> str:
        return "vector_db_creation"

    def get_dependencies(self) -> List[str]:
        return ["chunk_generation"]  # Depends on chunks being created

    def get_stage_description(self) -> str:
        return "Creates vector database collections from processed chunks with framework integration"

    async def initialize(self, context: Dict[str, Any]) -> bool:
        """Initialize the VectorDB stage with framework components"""
        try:
            print("🔧 Initializing VectorDB Pipeline Stage...")

            # Get framework components from context
            self.language_registry = context.get('language_registry')
            self.chunk_registry = context.get('chunk_registry')
            self.gpu_manager = context.get('gpu_manager')

            # Initialize VectorDB creator
            self.vector_db_creator = EnhancedVectorDBCreator()

            # Integrate with framework components
            framework_components = {
                'language_registry': self.language_registry,
                'chunk_registry': self.chunk_registry,
                'gpu_manager': self.gpu_manager
            }
            self.vector_db_creator.integrate_with_framework(framework_components)

            print("✅ VectorDB Pipeline Stage initialized")
            return True

        except Exception as e:
            print(f"❌ VectorDB Pipeline Stage initialization failed: {e}")
            return False

    async def process(self, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Process chunks and create vector database collections"""
        try:
            print("🔄 Processing VectorDB Pipeline Stage...")

            # Extract chunks from input data
            chunks = input_data.get('chunks', [])
            if not chunks:
                return {
                    'success': False,
                    'error': 'No chunks provided for vector database creation',
                    'stage': self.get_stage_name()
                }

            # Determine collection name
            collection_name = self.collection_name or input_data.get('codebase_name', 'default_collection')

            # Use GPU-distributed processing if available and enabled
            if self.gpu_distributed and self.gpu_manager:
                collection_info = await self._create_distributed_collection(chunks, collection_name, context)
            else:
                collection_info = await self._create_standard_collection(chunks, collection_name, context)

            return {
                'success': True,
                'collection_info': collection_info,
                'chunks_processed': len(chunks),
                'stage': self.get_stage_name(),
                'processing_time': collection_info.get('processing_time', 0)
            }

        except Exception as e:
            print(f"❌ VectorDB Pipeline Stage processing failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'stage': self.get_stage_name()
            }

    async def _create_standard_collection(self, chunks: List[Dict], collection_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Create collection using standard processing"""
        start_time = time.time()

        # Check if vector_db_creator is initialized
        if self.vector_db_creator is None:
            raise RuntimeError("VectorDB creator not initialized. Call initialize() first.")

        # Add framework metadata to chunks
        enhanced_chunks = self._enhance_chunks_with_framework_data(chunks, context)

        # Create collection
        collection_info = self.vector_db_creator.create_collection(enhanced_chunks, collection_name)

        processing_time = time.time() - start_time
        collection_info['processing_time'] = processing_time
        collection_info['processing_mode'] = 'standard'

        return collection_info

    async def _create_distributed_collection(self, chunks: List[Dict], collection_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Create collection using GPU-distributed processing"""
        start_time = time.time()

        # Check if vector_db_creator is initialized
        if self.vector_db_creator is None:
            raise RuntimeError("VectorDB creator not initialized. Call initialize() first.")

        try:
            # Get available GPUs
            available_gpus = await self.gpu_manager.discover_available_gpus()
            if not available_gpus:
                print("⚠️ No GPUs available, falling back to standard processing")
                return await self._create_standard_collection(chunks, collection_name, context)

            print(f"🚀 Using distributed processing with {len(available_gpus)} GPUs")

            # Split chunks across available GPUs
            chunk_batches = self._split_chunks_for_gpus(chunks, len(available_gpus))

            # Process batches in parallel across GPUs
            batch_results = await asyncio.gather(*[
                self._process_chunk_batch(batch, gpu_info, context)
                for batch, gpu_info in zip(chunk_batches, available_gpus.items())
            ])

            # Combine results
            all_enhanced_chunks = []
            for batch_result in batch_results:
                all_enhanced_chunks.extend(batch_result)

            # Create final collection
            collection_info = self.vector_db_creator.create_collection(all_enhanced_chunks, collection_name)

            processing_time = time.time() - start_time
            collection_info['processing_time'] = processing_time
            collection_info['processing_mode'] = 'gpu_distributed'
            collection_info['gpus_used'] = len(available_gpus)

            return collection_info

        except Exception as e:
            print(f"⚠️ Distributed processing failed: {e}, falling back to standard")
            return await self._create_standard_collection(chunks, collection_name, context)

    def _enhance_chunks_with_framework_data(self, chunks: List[Dict], context: Dict[str, Any]) -> List[Dict]:
        """Enhance chunks with framework-specific metadata"""
        enhanced_chunks = []

        for chunk in chunks:
            enhanced_chunk = chunk.copy()

            # Add language-specific metadata from registry
            if self.language_registry and 'file_path' in chunk:
                file_path = chunk['file_path']
                processor = self.language_registry.get_processor_for_file(file_path)
                if processor:
                    enhanced_chunk['framework_language'] = processor.get_language_name()
                    enhanced_chunk['language_processor'] = processor.__class__.__name__
                    enhanced_chunk['language_extensions'] = list(processor.get_supported_extensions())

            # Add chunk type metadata from registry
            if self.chunk_registry and 'chunk_type' in chunk:
                chunk_type = chunk['chunk_type']
                chunk_info = self.chunk_registry.get_chunk_info(chunk_type)
                if chunk_info:
                    enhanced_chunk['chunk_generator'] = chunk_info.get('generator_class', 'unknown')
                    enhanced_chunk['chunk_priority'] = chunk_info.get('priority', 1)

            # Add processing context metadata
            enhanced_chunk['framework_version'] = context.get('framework_version', 'unknown')
            enhanced_chunk['processing_timestamp'] = datetime.now().isoformat()
            enhanced_chunk['pipeline_stage'] = self.get_stage_name()

            enhanced_chunks.append(enhanced_chunk)

        return enhanced_chunks

    def _split_chunks_for_gpus(self, chunks: List[Dict], num_gpus: int) -> List[List[Dict]]:
        """Split chunks into batches for distributed GPU processing"""
        if num_gpus <= 1:
            return [chunks]

        batch_size = max(1, len(chunks) // num_gpus)
        batches = []

        for i in range(0, len(chunks), batch_size):
            batch = chunks[i:i + batch_size]
            batches.append(batch)

        # Ensure we don't have more batches than GPUs
        while len(batches) > num_gpus:
            # Merge the last two batches
            last_batch = batches.pop()
            batches[-1].extend(last_batch)

        return batches

    async def _process_chunk_batch(self, chunk_batch: List[Dict], gpu_info: tuple, context: Dict[str, Any]) -> List[Dict]:
        """Process a batch of chunks on a specific GPU"""
        gpu_host, gpu_data = gpu_info

        try:
            print(f"🔄 Processing {len(chunk_batch)} chunks on {gpu_data['type']} at {gpu_host}")

            # For now, just enhance with GPU-specific metadata
            # In future phases, this could use GPU-specific embedding generation
            enhanced_batch = []
            for chunk in chunk_batch:
                enhanced_chunk = chunk.copy()
                enhanced_chunk['processing_gpu'] = gpu_data['type']
                enhanced_chunk['processing_host'] = gpu_host
                enhanced_chunk['gpu_tier'] = gpu_data.get('tier', 'unknown')
                enhanced_batch.append(enhanced_chunk)

            return enhanced_batch

        except Exception as e:
            print(f"⚠️ GPU batch processing failed on {gpu_host}: {e}")
            return chunk_batch  # Return original batch if processing fails

# Maintain backward compatibility
VectorDBCreator = EnhancedVectorDBCreator